# MCP Rules Engine Terraform Variables
# Copy this file to terraform.tfvars and update with your actual values

# Required: Production Cloud Run URL for MCP Rules Engine
cloud_run_url = "https://mcp-prod-************.us-central1.run.app"

# Project ID for MCP Rules Engine (texas-laws-personalinjury)
project_id = "texas-laws-personalinjury"

# Optional: Override default region
# region = "us-central1"

# Note: Tenant-specific resources are managed manually
# See docs/NOTE_cross_project.md for details

# Optional: Custom domain for API Gateway
# api_gateway_domain = "rules.ailexlaw.com"

# Optional: Override backend service account
backend_service_account = "<EMAIL>"

# Optional: Environment and monitoring settings
# environment = "production"
# enable_monitoring = true
# gateway_timeout = 30
# rate_limit_per_minute = 100

