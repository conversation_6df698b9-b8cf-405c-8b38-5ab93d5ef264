# Google API Key Management for MCP Rules Engine
# Note: Tenant-specific API keys are managed manually due to cross-project permission constraints
# See docs/NOTE_cross_project.md for details

# Note: API key creation and Secret Manager resources for tenants are managed manually
# This file only contains infrastructure that can be managed via Terraform with current permissions

# Note: IAM roles, service accounts, and key rotation infrastructure
# are managed manually due to cross-project permission constraints
