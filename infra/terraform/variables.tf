# MCP Rules Engine Infrastructure Variables
# Centralized variable definitions for Terraform configuration

variable "project_id" {
  description = "Google Cloud Project ID for MCP Rules Engine (texas-laws-personalinjury)"
  type        = string
  default     = "texas-laws-personalinjury"
}

variable "region" {
  description = "Google Cloud region for resource deployment"
  type        = string
  default     = "us-central1"
}

# Note: Tenant-specific resources (API keys, secrets) are managed manually
# due to cross-project permission constraints. See docs/NOTE_cross_project.md

variable "cloud_run_url" {
  description = "Production Cloud Run base URL for the MCP Rules Engine service"
  type        = string
  # This should be set via terraform.tfvars or command line
  # Example: https://mcp-rules-prod-abc123-uc.a.run.app
}

variable "api_gateway_domain" {
  description = "Custom domain for the API Gateway (optional)"
  type        = string
  default     = "rules.ailexlaw.com"
}

variable "backend_service_account" {
  description = "Service account email for Cloud Run invoker permissions"
  type        = string
  default     = "<EMAIL>"
}

variable "environment" {
  description = "Environment name (production, staging, development)"
  type        = string
  default     = "production"
}

variable "enable_monitoring" {
  description = "Enable monitoring and alerting resources"
  type        = bool
  default     = true
}

variable "gateway_timeout" {
  description = "API Gateway timeout in seconds"
  type        = number
  default     = 30
}

variable "rate_limit_per_minute" {
  description = "Rate limit per API key per minute"
  type        = number
  default     = 100
}
