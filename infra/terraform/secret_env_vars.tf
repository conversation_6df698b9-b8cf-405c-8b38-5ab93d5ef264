# Environment Variable Secrets Configuration
# Note: Environment variable secrets are managed manually due to cross-project permission constraints
# See docs/NOTE_cross_project.md for details

# Manual creation commands:
# gcloud secrets create MCP_RULES_BASE --project=newtexaslaw-1738585844702 --data-file=<(echo "https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev")
# gcloud secrets create MCP_RULES_BASE_STG --project=newtexaslaw-1738585844702 --data-file=<(echo "https://mcp-rules-gateway-staging-xyz.uc.gateway.dev")

# Note: All environment variable secrets, secret versions, and IAM bindings are managed manually
