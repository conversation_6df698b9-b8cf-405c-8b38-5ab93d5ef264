# Environment Variable Secrets Configuration
# Note: Environment variable secrets are managed manually due to cross-project permission constraints
# See docs/NOTE_cross_project.md for details

# Manual creation commands:
# gcloud secrets create MCP_RULES_BASE --project=newtexaslaw-1738585844702 --data-file=<(echo "https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev")
# gcloud secrets create MCP_RULES_BASE_STG --project=newtexaslaw-1738585844702 --data-file=<(echo "https://mcp-rules-gateway-staging-xyz.uc.gateway.dev")

# Default production value for MCP Rules Base URL
resource "google_secret_manager_secret_version" "mcp_rules_base_prod" {
  provider = google.tenant

  secret      = google_secret_manager_secret.mcp_rules_base.id
  secret_data = "https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev"

  lifecycle {
    ignore_changes = [secret_data]
  }
}

# MCP Rules Base URL secret for staging
resource "google_secret_manager_secret" "mcp_rules_base_staging" {
  provider = google.tenant

  secret_id = "MCP_RULES_BASE_STG"

  labels = {
    service     = "core-ailex"
    environment = "staging"
    managed_by  = "terraform"
    type        = "env-var"
  }

  replication {
    auto {}
  }

  # Note: Secret Manager API must be enabled manually due to permission constraints
}

# Default staging value for MCP Rules Base URL
resource "google_secret_manager_secret_version" "mcp_rules_base_staging_value" {
  provider = google.tenant

  secret      = google_secret_manager_secret.mcp_rules_base_staging.id
  secret_data = "https://mcp-rules-gateway-staging-1k6gjpoj.uc.gateway.dev"

  lifecycle {
    ignore_changes = [secret_data]
  }
}

# IAM binding for Core AiLex service accounts to access environment variable secrets
resource "google_secret_manager_secret_iam_binding" "env_vars_accessor" {
  provider = google.tenant

  secret_id = google_secret_manager_secret.mcp_rules_base.secret_id
  role      = "roles/secretmanager.secretAccessor"

  members = [
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
  ]
}

resource "google_secret_manager_secret_iam_binding" "env_vars_staging_accessor" {
  provider = google.tenant

  secret_id = google_secret_manager_secret.mcp_rules_base_staging.secret_id
  role      = "roles/secretmanager.secretAccessor"

  members = [
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
    "serviceAccount:<EMAIL>",
  ]
}

# Output the secret names for reference
output "env_var_secret_names" {
  description = "Names of environment variable secrets"
  value = {
    mcp_rules_base_prod    = google_secret_manager_secret.mcp_rules_base.name
    mcp_rules_base_staging = google_secret_manager_secret.mcp_rules_base_staging.name
  }
}

output "env_var_secret_ids" {
  description = "Full resource names of environment variable secrets"
  value = {
    mcp_rules_base_prod    = google_secret_manager_secret.mcp_rules_base.id
    mcp_rules_base_staging = google_secret_manager_secret.mcp_rules_base_staging.id
  }
}
