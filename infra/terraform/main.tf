# MCP Rules Engine Infrastructure Configuration
# Main Terraform configuration for MCP Rules Engine integration

# Terraform configuration moved to providers.tf

# Configure the Google Cloud Provider
provider "google" {
  project = var.project_id
  region  = var.region
}

# Configure the Google Cloud Beta Provider for API Gateway
provider "google-beta" {
  project = var.project_id
  region  = var.region
}

# Variables are defined in variables.tf
# Example values are in terraform.tfvars.example
