# MCP Rules Engine Production API Gateway Infrastructure
# Terraform configuration for Google Cloud API Gateway production deployment
# Note: Variables are defined in main.tf

# Configure provider for MCP project (texas-laws-personalinjury)
provider "google" {
  alias   = "mcp"
  project = var.mcp_project_id
  region  = var.region
}

# Configure google-beta provider for API Gateway resources
provider "google-beta" {
  alias   = "mcp"
  project = var.mcp_project_id
  region  = var.region
}

# Enable required APIs in MCP project
resource "google_project_service" "apigateway" {
  provider = google.mcp
  project  = var.mcp_project_id
  service  = "apigateway.googleapis.com"

  disable_dependent_services = true
  disable_on_destroy         = false
}

resource "google_project_service" "servicecontrol" {
  provider = google.mcp
  project  = var.mcp_project_id
  service  = "servicecontrol.googleapis.com"

  disable_dependent_services = true
  disable_on_destroy         = false
}

resource "google_project_service" "servicemanagement" {
  provider = google.mcp
  project  = var.mcp_project_id
  service  = "servicemanagement.googleapis.com"

  disable_dependent_services = true
  disable_on_destroy         = false
}

# Create the API Gateway API
resource "google_api_gateway_api" "mcp_rules_api" {
  provider = google-beta.mcp

  api_id       = "mcp-rules-gateway"
  display_name = "MCP Rules Engine API"

  labels = {
    service     = "mcp-rules-engine"
    environment = "production"
    managed_by  = "terraform"
  }

  depends_on = [
    google_project_service.apigateway,
    google_project_service.servicecontrol,
    google_project_service.servicemanagement
  ]
}

# Process the OpenAPI spec template with Cloud Run URL using native templatefile()
locals {
  openapi_prod = templatefile(
    "${path.module}/../../api/mcp-rules-config-prod.yaml",
    {
      CLOUD_RUN_URL = var.cloud_run_url
    }
  )
}

# Create API Gateway configuration
resource "google_api_gateway_api_config" "mcp_rules_config" {
  provider = google-beta.mcp

  api           = google_api_gateway_api.mcp_rules_api.api_id
  api_config_id = "mcp-rules-config-prod"
  display_name  = "MCP Rules Engine Production Config"

  openapi_documents {
    document {
      path     = "openapi.yaml"
      contents = base64encode(local.openapi_prod)
    }
  }

  gateway_config {
    backend_config {
      google_service_account = var.backend_service_account
    }
  }

  labels = {
    service     = "mcp-rules-engine"
    environment = "production"
    managed_by  = "terraform"
    version     = "v1"
  }

  lifecycle {
    create_before_destroy = true
  }

  depends_on = [
    google_api_gateway_api.mcp_rules_api
  ]
}

# Create the API Gateway
resource "google_api_gateway_gateway" "prod" {
  provider = google-beta.mcp

  api_config   = google_api_gateway_api_config.mcp_rules_config.id
  gateway_id   = "mcp-rules-gateway-prod"
  display_name = "MCP Rules Engine Production Gateway"
  region       = var.region

  labels = {
    service     = "mcp-rules-engine"
    environment = "production"
    managed_by  = "terraform"
  }

  depends_on = [
    google_api_gateway_api_config.mcp_rules_config
  ]
}

# Create a managed SSL certificate for the custom domain (optional)
resource "google_compute_managed_ssl_certificate" "mcp_rules_ssl" {
  provider = google.mcp

  name = "mcp-rules-gateway-ssl"

  managed {
    domains = [var.api_gateway_domain]
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Output important values
output "prod_gateway_host" {
  description = "The hostname of the production API Gateway"
  value       = google_api_gateway_gateway.prod.default_hostname
}

output "api_gateway_url" {
  description = "The full URL of the API Gateway"
  value       = "https://${google_api_gateway_gateway.prod.default_hostname}"
}

output "api_gateway_id" {
  description = "The ID of the API Gateway"
  value       = google_api_gateway_gateway.prod.gateway_id
}

output "api_config_id" {
  description = "The ID of the API Gateway configuration"
  value       = google_api_gateway_api_config.mcp_rules_config.api_config_id
}

output "ssl_certificate_id" {
  description = "The ID of the managed SSL certificate"
  value       = google_compute_managed_ssl_certificate.mcp_rules_ssl.id
}

# IAM binding for Cloud Run invoker (if needed)
resource "google_cloud_run_service_iam_member" "gateway_invoker" {
  provider = google.mcp

  location = var.region
  project  = var.mcp_project_id
  service  = "mcp-prod"  # Adjust to match your Cloud Run service name
  role     = "roles/run.invoker"
  member   = "serviceAccount:${var.backend_service_account}"
}

# Monitoring and alerting resources
resource "google_monitoring_alert_policy" "gateway_error_rate" {
  provider = google.mcp

  display_name = "MCP Rules Gateway - High Error Rate"
  combiner     = "OR"

  conditions {
    display_name = "Error rate > 2%"

    condition_threshold {
      filter          = "resource.type=\"api\" AND resource.labels.service=\"mcp-rules-gateway\""
      duration        = "300s"
      comparison      = "COMPARISON_GT"
      threshold_value = 0.02

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_MEAN"
        group_by_fields = ["resource.label.service"]
      }
    }
  }

  notification_channels = []  # Add notification channels as needed

  alert_strategy {
    auto_close = "1800s"
  }
}

resource "google_monitoring_alert_policy" "gateway_latency" {
  provider = google.mcp

  display_name = "MCP Rules Gateway - High Latency"
  combiner     = "OR"

  conditions {
    display_name = "P95 latency > 700ms"

    condition_threshold {
      filter          = "resource.type=\"api\" AND resource.labels.service=\"mcp-rules-gateway\""
      duration        = "300s"
      comparison      = "COMPARISON_GT"
      threshold_value = 700

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_DELTA"
        cross_series_reducer = "REDUCE_PERCENTILE_95"
        group_by_fields = ["resource.label.service"]
      }
    }
  }

  notification_channels = []  # Add notification channels as needed

  alert_strategy {
    auto_close = "1800s"
  }
}
