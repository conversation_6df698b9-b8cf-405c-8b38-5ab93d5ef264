{"version": 4, "terraform_version": "1.5.7", "serial": 134, "lineage": "ca5901af-c120-8608-db7b-51fbfa241a41", "outputs": {"api_config_id": {"value": "mcp-rules-config-prod", "type": "string"}, "api_gateway_id": {"value": "mcp-rules-gateway-prod", "type": "string"}, "api_gateway_url": {"value": "https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev", "type": "string"}, "env_var_secret_ids": {"value": {"mcp_rules_base_prod": null, "mcp_rules_base_staging": null}, "type": ["object", {"mcp_rules_base_prod": "string", "mcp_rules_base_staging": "string"}]}, "env_var_secret_names": {"value": {"mcp_rules_base_prod": null, "mcp_rules_base_staging": null}, "type": ["object", {"mcp_rules_base_prod": "string", "mcp_rules_base_staging": "string"}]}, "mcp_api_key_names": {"value": {"demo-tenant": "mcp-rules-api-demo-tenant", "pilot-smith": "mcp-rules-api-pilot-smith", "sandbox-test": "mcp-rules-api-sandbox-test"}, "type": ["object", {"demo-tenant": "string", "pilot-smith": "string", "sandbox-test": "string"}]}, "mcp_key_rotator_email": {"value": null, "type": "string"}, "mcp_secret_ids": {"value": {"demo-tenant": null, "pilot-smith": null, "sandbox-test": null}, "type": ["object", {"demo-tenant": "dynamic", "pilot-smith": "dynamic", "sandbox-test": "dynamic"}]}, "mcp_secret_names": {"value": {"demo-tenant": null, "pilot-smith": null, "sandbox-test": null}, "type": ["object", {"demo-tenant": "dynamic", "pilot-smith": "dynamic", "sandbox-test": "dynamic"}]}, "prod_gateway_host": {"value": "mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev", "type": "string"}, "ssl_certificate_id": {"value": null, "type": "string"}}, "resources": [{"mode": "managed", "type": "google_api_gateway_api", "name": "mcp_rules_api", "provider": "provider[\"registry.terraform.io/hashicorp/google-beta\"].mcp", "instances": [{"schema_version": 0, "attributes": {"api_id": "mcp-rules-gateway", "create_time": "2025-06-12T10:05:15.989929003Z", "display_name": "MCP Rules Engine API", "effective_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "id": "projects/texas-laws-personalinjury/locations/global/apis/mcp-rules-gateway", "labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "managed_service": "mcp-rules-gateway-3jnyyfifdjilf.apigateway.texas-laws-personalinjury.cloud.goog", "name": "projects/texas-laws-personalinjury/locations/global/apis/mcp-rules-gateway", "project": "texas-laws-personalinjury", "terraform_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["google_project_service.apigateway", "google_project_service.servicecontrol", "google_project_service.servicemanagement"], "create_before_destroy": true}]}, {"mode": "managed", "type": "google_api_gateway_gateway", "name": "prod", "provider": "provider[\"registry.terraform.io/hashicorp/google-beta\"].mcp", "instances": [{"schema_version": 0, "attributes": {"api_config": "projects/122290401475/locations/global/apis/mcp-rules-gateway/configs/mcp-rules-config-prod-final", "default_hostname": "mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev", "display_name": "MCP Rules Gateway Production", "effective_labels": {}, "gateway_id": "mcp-rules-gateway-prod", "id": "projects/texas-laws-personalinjury/locations/us-central1/gateways/mcp-rules-gateway-prod", "labels": {}, "name": "projects/texas-laws-personalinjury/locations/us-central1/gateways/mcp-rules-gateway-prod", "project": "texas-laws-personalinjury", "region": "us-central1", "terraform_labels": {}, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMCJ9", "dependencies": ["google_api_gateway_api.mcp_rules_api", "google_api_gateway_api_config.mcp_rules_config", "google_project_service.apigateway", "google_project_service.servicecontrol", "google_project_service.servicemanagement"]}]}, {"mode": "managed", "type": "google_apikeys_key", "name": "mcp_api_keys", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_cloud_run_service_iam_member", "name": "function_invoker", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": []}, {"mode": "managed", "type": "google_cloud_run_service_iam_member", "name": "gateway_invoker", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY4HLxGEyk=", "id": "v1/projects/texas-laws-personalinjury/locations/us-central1/services/mcp-prod/roles/run.invoker/serviceAccount:<EMAIL>", "location": "us-central1", "member": "serviceAccount:<EMAIL>", "project": "texas-laws-personalinjury", "role": "roles/run.invoker", "service": "v1/projects/texas-laws-personalinjury/locations/us-central1/services/mcp-prod"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "google_cloudfunctions2_function", "name": "tenant_onboard", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "function_apikeys_admin", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "function_firestore_user", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "function_run_invoker", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].tenant_project", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "function_secretmanager_admin", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "mcp_key_rotator_binding", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "provisioner_apikeys_admin", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp_project", "instances": []}, {"mode": "managed", "type": "google_project_iam_member", "name": "provisioner_secretmanager_admin", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp_project", "instances": []}, {"mode": "managed", "type": "google_project_service", "name": "apigateway", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp", "instances": [{"schema_version": 0, "attributes": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/apigateway.googleapis.com", "project": "texas-laws-personalinjury", "service": "apigateway.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInJlYWQiOjYwMDAwMDAwMDAwMCwidXBkYXRlIjoxMjAwMDAwMDAwMDAwfX0=", "create_before_destroy": true}]}, {"mode": "managed", "type": "google_project_service", "name": "servicecontrol", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp", "instances": [{"schema_version": 0, "attributes": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/servicecontrol.googleapis.com", "project": "texas-laws-personalinjury", "service": "servicecontrol.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInJlYWQiOjYwMDAwMDAwMDAwMCwidXBkYXRlIjoxMjAwMDAwMDAwMDAwfX0=", "create_before_destroy": true}]}, {"mode": "managed", "type": "google_project_service", "name": "servicemanagement", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp", "instances": [{"schema_version": 0, "attributes": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/servicemanagement.googleapis.com", "project": "texas-laws-personalinjury", "service": "servicemanagement.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInJlYWQiOjYwMDAwMDAwMDAwMCwidXBkYXRlIjoxMjAwMDAwMDAwMDAwfX0=", "create_before_destroy": true}]}, {"mode": "managed", "type": "google_secret_manager_secret", "name": "mcp_api_keys", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret", "name": "mcp_rules_base", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret", "name": "mcp_rules_base_staging", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_iam_binding", "name": "env_vars_accessor", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_iam_binding", "name": "env_vars_staging_accessor", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_iam_binding", "name": "mcp_secret_accessor", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_version", "name": "mcp_api_key_versions", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_version", "name": "mcp_initial_versions", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_version", "name": "mcp_rules_base_prod", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_version", "name": "mcp_rules_base_staging_value", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_secret_manager_secret_version", "name": "rotator_sa_key_version", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}, {"mode": "managed", "type": "google_service_account_iam_member", "name": "allow_impersonation", "provider": "provider[\"registry.terraform.io/hashicorp/google\"].mcp_project", "instances": []}, {"mode": "managed", "type": "google_service_account_key", "name": "mcp_key_rotator_key", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": []}], "check_results": null}