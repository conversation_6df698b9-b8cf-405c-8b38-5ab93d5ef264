{"format_version": "1.2", "terraform_version": "1.5.7", "variables": {"api_gateway_domain": {"value": "rules.ailexlaw.com"}, "backend_service_account": {"value": "<EMAIL>"}, "cloud_run_url": {"value": "https://mcp-rules-prod-abc123-uc.a.run.app"}, "enable_monitoring": {"value": true}, "environment": {"value": "production"}, "gateway_timeout": {"value": 30}, "project_id": {"value": "texas-laws-personalinjury"}, "rate_limit_per_minute": {"value": 100}, "region": {"value": "us-central1"}}, "planned_values": {"outputs": {"api_config_id": {"sensitive": false, "type": "string", "value": "mcp-rules-config-prod"}, "api_gateway_id": {"sensitive": false, "type": "string", "value": "mcp-rules-gateway-prod"}, "api_gateway_url": {"sensitive": false, "type": "string", "value": "https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev"}, "prod_gateway_host": {"sensitive": false, "type": "string", "value": "mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev"}, "ssl_certificate_id": {"sensitive": false}}, "root_module": {"resources": [{"address": "google_api_gateway_api.mcp_rules_api", "mode": "managed", "type": "google_api_gateway_api", "name": "mcp_rules_api", "provider_name": "registry.terraform.io/hashicorp/google-beta", "schema_version": 0, "values": {"api_id": "mcp-rules-gateway", "create_time": "2025-06-12T10:05:15.989929003Z", "display_name": "MCP Rules Engine API", "effective_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "id": "projects/texas-laws-personalinjury/locations/global/apis/mcp-rules-gateway", "labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "managed_service": "mcp-rules-gateway-3jnyyfifdjilf.apigateway.texas-laws-personalinjury.cloud.goog", "name": "projects/texas-laws-personalinjury/locations/global/apis/mcp-rules-gateway", "project": "texas-laws-personalinjury", "terraform_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "timeouts": null}, "sensitive_values": {"effective_labels": {}, "labels": {}, "terraform_labels": {}}}, {"address": "google_api_gateway_api_config.mcp_rules_config", "mode": "managed", "type": "google_api_gateway_api_config", "name": "mcp_rules_config", "provider_name": "registry.terraform.io/hashicorp/google-beta", "schema_version": 0, "values": {"api": "mcp-rules-gateway", "api_config_id": "mcp-rules-config-prod", "display_name": "MCP Rules Engine Production Config", "effective_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine", "version": "v1"}, "gateway_config": [{"backend_config": [{"google_service_account": "<EMAIL>"}]}], "grpc_services": [], "labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine", "version": "v1"}, "managed_service_configs": [], "openapi_documents": [{"document": [{"contents": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "path": "openapi.yaml"}]}], "project": "texas-laws-personalinjury", "terraform_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine", "version": "v1"}, "timeouts": null}, "sensitive_values": {"effective_labels": {}, "gateway_config": [{"backend_config": [{}]}], "grpc_services": [], "labels": {}, "managed_service_configs": [], "openapi_documents": [{"document": [{}]}], "terraform_labels": {}}}, {"address": "google_api_gateway_gateway.prod", "mode": "managed", "type": "google_api_gateway_gateway", "name": "prod", "provider_name": "registry.terraform.io/hashicorp/google-beta", "schema_version": 0, "values": {"default_hostname": "mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev", "display_name": "MCP Rules Engine Production Gateway", "effective_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "gateway_id": "mcp-rules-gateway-prod", "id": "projects/texas-laws-personalinjury/locations/us-central1/gateways/mcp-rules-gateway-prod", "labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "name": "projects/texas-laws-personalinjury/locations/us-central1/gateways/mcp-rules-gateway-prod", "project": "texas-laws-personalinjury", "region": "us-central1", "terraform_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "timeouts": null}, "sensitive_values": {"effective_labels": {}, "labels": {}, "terraform_labels": {}}}, {"address": "google_cloud_run_service_iam_member.gateway_invoker", "mode": "managed", "type": "google_cloud_run_service_iam_member", "name": "gateway_invoker", "provider_name": "registry.terraform.io/hashicorp/google", "schema_version": 0, "values": {"condition": [], "etag": "BwY4HLxGEyk=", "id": "v1/projects/texas-laws-personalinjury/locations/us-central1/services/mcp-prod/roles/run.invoker/serviceAccount:<EMAIL>", "location": "us-central1", "member": "serviceAccount:<EMAIL>", "project": "texas-laws-personalinjury", "role": "roles/run.invoker", "service": "v1/projects/texas-laws-personalinjury/locations/us-central1/services/mcp-prod"}, "sensitive_values": {"condition": []}}, {"address": "google_compute_managed_ssl_certificate.mcp_rules_ssl", "mode": "managed", "type": "google_compute_managed_ssl_certificate", "name": "mcp_rules_ssl", "provider_name": "registry.terraform.io/hashicorp/google", "schema_version": 0, "values": {"description": null, "managed": [{"domains": ["rules.ailexlaw.com"]}], "name": "mcp-rules-gateway-ssl", "project": "texas-laws-personalinjury", "timeouts": null, "type": "MANAGED"}, "sensitive_values": {"managed": [{"domains": [false]}], "subject_alternative_names": []}}, {"address": "google_monitoring_alert_policy.gateway_error_rate", "mode": "managed", "type": "google_monitoring_alert_policy", "name": "gateway_error_rate", "provider_name": "registry.terraform.io/hashicorp/google", "schema_version": 0, "values": {"alert_strategy": [{"auto_close": "1800s", "notification_channel_strategy": [], "notification_rate_limit": []}], "combiner": "OR", "conditions": [{"condition_absent": [], "condition_matched_log": [], "condition_monitoring_query_language": [], "condition_prometheus_query_language": [], "condition_threshold": [{"aggregations": [{"alignment_period": "60s", "cross_series_reducer": "REDUCE_MEAN", "group_by_fields": ["resource.label.service"], "per_series_aligner": "ALIGN_RATE"}], "comparison": "COMPARISON_GT", "denominator_aggregations": [], "denominator_filter": null, "duration": "300s", "evaluation_missing_data": null, "filter": "resource.type=\"api\" AND resource.labels.service=\"mcp-rules-gateway\"", "forecast_options": [], "threshold_value": 0.02, "trigger": []}], "display_name": "Error rate > 2%"}], "display_name": "MCP Rules Gateway - High Error Rate", "documentation": [], "enabled": true, "notification_channels": [], "project": "texas-laws-personalinjury", "severity": null, "timeouts": null, "user_labels": null}, "sensitive_values": {"alert_strategy": [{"notification_channel_strategy": [], "notification_rate_limit": []}], "conditions": [{"condition_absent": [], "condition_matched_log": [], "condition_monitoring_query_language": [], "condition_prometheus_query_language": [], "condition_threshold": [{"aggregations": [{"group_by_fields": [false]}], "denominator_aggregations": [], "forecast_options": [], "trigger": []}]}], "creation_record": [], "documentation": [], "notification_channels": []}}, {"address": "google_monitoring_alert_policy.gateway_latency", "mode": "managed", "type": "google_monitoring_alert_policy", "name": "gateway_latency", "provider_name": "registry.terraform.io/hashicorp/google", "schema_version": 0, "values": {"alert_strategy": [{"auto_close": "1800s", "notification_channel_strategy": [], "notification_rate_limit": []}], "combiner": "OR", "conditions": [{"condition_absent": [], "condition_matched_log": [], "condition_monitoring_query_language": [], "condition_prometheus_query_language": [], "condition_threshold": [{"aggregations": [{"alignment_period": "60s", "cross_series_reducer": "REDUCE_PERCENTILE_95", "group_by_fields": ["resource.label.service"], "per_series_aligner": "ALIGN_DELTA"}], "comparison": "COMPARISON_GT", "denominator_aggregations": [], "denominator_filter": null, "duration": "300s", "evaluation_missing_data": null, "filter": "resource.type=\"api\" AND resource.labels.service=\"mcp-rules-gateway\"", "forecast_options": [], "threshold_value": 700, "trigger": []}], "display_name": "P95 latency > 700ms"}], "display_name": "MCP Rules Gateway - High Latency", "documentation": [], "enabled": true, "notification_channels": [], "project": "texas-laws-personalinjury", "severity": null, "timeouts": null, "user_labels": null}, "sensitive_values": {"alert_strategy": [{"notification_channel_strategy": [], "notification_rate_limit": []}], "conditions": [{"condition_absent": [], "condition_matched_log": [], "condition_monitoring_query_language": [], "condition_prometheus_query_language": [], "condition_threshold": [{"aggregations": [{"group_by_fields": [false]}], "denominator_aggregations": [], "forecast_options": [], "trigger": []}]}], "creation_record": [], "documentation": [], "notification_channels": []}}, {"address": "google_project_service.apigateway", "mode": "managed", "type": "google_project_service", "name": "apigateway", "provider_name": "registry.terraform.io/hashicorp/google", "schema_version": 0, "values": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/apigateway.googleapis.com", "project": "texas-laws-personalinjury", "service": "apigateway.googleapis.com", "timeouts": null}, "sensitive_values": {}}, {"address": "google_project_service.servicecontrol", "mode": "managed", "type": "google_project_service", "name": "servicecontrol", "provider_name": "registry.terraform.io/hashicorp/google", "schema_version": 0, "values": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/servicecontrol.googleapis.com", "project": "texas-laws-personalinjury", "service": "servicecontrol.googleapis.com", "timeouts": null}, "sensitive_values": {}}, {"address": "google_project_service.servicemanagement", "mode": "managed", "type": "google_project_service", "name": "servicemanagement", "provider_name": "registry.terraform.io/hashicorp/google", "schema_version": 0, "values": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/servicemanagement.googleapis.com", "project": "texas-laws-personalinjury", "service": "servicemanagement.googleapis.com", "timeouts": null}, "sensitive_values": {}}]}}, "resource_changes": [{"address": "google_api_gateway_api.mcp_rules_api", "mode": "managed", "type": "google_api_gateway_api", "name": "mcp_rules_api", "provider_name": "registry.terraform.io/hashicorp/google-beta", "change": {"actions": ["no-op"], "before": {"api_id": "mcp-rules-gateway", "create_time": "2025-06-12T10:05:15.989929003Z", "display_name": "MCP Rules Engine API", "effective_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "id": "projects/texas-laws-personalinjury/locations/global/apis/mcp-rules-gateway", "labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "managed_service": "mcp-rules-gateway-3jnyyfifdjilf.apigateway.texas-laws-personalinjury.cloud.goog", "name": "projects/texas-laws-personalinjury/locations/global/apis/mcp-rules-gateway", "project": "texas-laws-personalinjury", "terraform_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "timeouts": null}, "after": {"api_id": "mcp-rules-gateway", "create_time": "2025-06-12T10:05:15.989929003Z", "display_name": "MCP Rules Engine API", "effective_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "id": "projects/texas-laws-personalinjury/locations/global/apis/mcp-rules-gateway", "labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "managed_service": "mcp-rules-gateway-3jnyyfifdjilf.apigateway.texas-laws-personalinjury.cloud.goog", "name": "projects/texas-laws-personalinjury/locations/global/apis/mcp-rules-gateway", "project": "texas-laws-personalinjury", "terraform_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "timeouts": null}, "after_unknown": {}, "before_sensitive": {"effective_labels": {}, "labels": {}, "terraform_labels": {}}, "after_sensitive": {"effective_labels": {}, "labels": {}, "terraform_labels": {}}}}, {"address": "google_api_gateway_api_config.mcp_rules_config", "mode": "managed", "type": "google_api_gateway_api_config", "name": "mcp_rules_config", "provider_name": "registry.terraform.io/hashicorp/google-beta", "change": {"actions": ["create"], "before": null, "after": {"api": "mcp-rules-gateway", "api_config_id": "mcp-rules-config-prod", "display_name": "MCP Rules Engine Production Config", "effective_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine", "version": "v1"}, "gateway_config": [{"backend_config": [{"google_service_account": "<EMAIL>"}]}], "grpc_services": [], "labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine", "version": "v1"}, "managed_service_configs": [], "openapi_documents": [{"document": [{"contents": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "path": "openapi.yaml"}]}], "project": "texas-laws-personalinjury", "terraform_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine", "version": "v1"}, "timeouts": null}, "after_unknown": {"api_config_id_prefix": true, "effective_labels": {}, "gateway_config": [{"backend_config": [{}]}], "grpc_services": [], "id": true, "labels": {}, "managed_service_configs": [], "name": true, "openapi_documents": [{"document": [{}]}], "service_config_id": true, "terraform_labels": {}}, "before_sensitive": false, "after_sensitive": {"effective_labels": {}, "gateway_config": [{"backend_config": [{}]}], "grpc_services": [], "labels": {}, "managed_service_configs": [], "openapi_documents": [{"document": [{}]}], "terraform_labels": {}}}}, {"address": "google_api_gateway_gateway.prod", "mode": "managed", "type": "google_api_gateway_gateway", "name": "prod", "provider_name": "registry.terraform.io/hashicorp/google-beta", "change": {"actions": ["update"], "before": {"api_config": "projects/122290401475/locations/global/apis/mcp-rules-gateway/configs/mcp-rules-config-prod-final", "default_hostname": "mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev", "display_name": "MCP Rules Gateway Production", "effective_labels": {}, "gateway_id": "mcp-rules-gateway-prod", "id": "projects/texas-laws-personalinjury/locations/us-central1/gateways/mcp-rules-gateway-prod", "labels": {}, "name": "projects/texas-laws-personalinjury/locations/us-central1/gateways/mcp-rules-gateway-prod", "project": "texas-laws-personalinjury", "region": "us-central1", "terraform_labels": {}, "timeouts": null}, "after": {"default_hostname": "mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev", "display_name": "MCP Rules Engine Production Gateway", "effective_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "gateway_id": "mcp-rules-gateway-prod", "id": "projects/texas-laws-personalinjury/locations/us-central1/gateways/mcp-rules-gateway-prod", "labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "name": "projects/texas-laws-personalinjury/locations/us-central1/gateways/mcp-rules-gateway-prod", "project": "texas-laws-personalinjury", "region": "us-central1", "terraform_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "timeouts": null}, "after_unknown": {"api_config": true, "effective_labels": {}, "labels": {}, "terraform_labels": {}}, "before_sensitive": {"effective_labels": {}, "labels": {}, "terraform_labels": {}}, "after_sensitive": {"effective_labels": {}, "labels": {}, "terraform_labels": {}}}}, {"address": "google_cloud_run_service_iam_member.gateway_invoker", "mode": "managed", "type": "google_cloud_run_service_iam_member", "name": "gateway_invoker", "provider_name": "registry.terraform.io/hashicorp/google", "change": {"actions": ["no-op"], "before": {"condition": [], "etag": "BwY4HLxGEyk=", "id": "v1/projects/texas-laws-personalinjury/locations/us-central1/services/mcp-prod/roles/run.invoker/serviceAccount:<EMAIL>", "location": "us-central1", "member": "serviceAccount:<EMAIL>", "project": "texas-laws-personalinjury", "role": "roles/run.invoker", "service": "v1/projects/texas-laws-personalinjury/locations/us-central1/services/mcp-prod"}, "after": {"condition": [], "etag": "BwY4HLxGEyk=", "id": "v1/projects/texas-laws-personalinjury/locations/us-central1/services/mcp-prod/roles/run.invoker/serviceAccount:<EMAIL>", "location": "us-central1", "member": "serviceAccount:<EMAIL>", "project": "texas-laws-personalinjury", "role": "roles/run.invoker", "service": "v1/projects/texas-laws-personalinjury/locations/us-central1/services/mcp-prod"}, "after_unknown": {}, "before_sensitive": {"condition": []}, "after_sensitive": {"condition": []}}}, {"address": "google_compute_managed_ssl_certificate.mcp_rules_ssl", "mode": "managed", "type": "google_compute_managed_ssl_certificate", "name": "mcp_rules_ssl", "provider_name": "registry.terraform.io/hashicorp/google", "change": {"actions": ["create"], "before": null, "after": {"description": null, "managed": [{"domains": ["rules.ailexlaw.com"]}], "name": "mcp-rules-gateway-ssl", "project": "texas-laws-personalinjury", "timeouts": null, "type": "MANAGED"}, "after_unknown": {"certificate_id": true, "creation_timestamp": true, "expire_time": true, "id": true, "managed": [{"domains": [false]}], "self_link": true, "subject_alternative_names": true}, "before_sensitive": false, "after_sensitive": {"managed": [{"domains": [false]}], "subject_alternative_names": []}}}, {"address": "google_monitoring_alert_policy.gateway_error_rate", "mode": "managed", "type": "google_monitoring_alert_policy", "name": "gateway_error_rate", "provider_name": "registry.terraform.io/hashicorp/google", "change": {"actions": ["create"], "before": null, "after": {"alert_strategy": [{"auto_close": "1800s", "notification_channel_strategy": [], "notification_rate_limit": []}], "combiner": "OR", "conditions": [{"condition_absent": [], "condition_matched_log": [], "condition_monitoring_query_language": [], "condition_prometheus_query_language": [], "condition_threshold": [{"aggregations": [{"alignment_period": "60s", "cross_series_reducer": "REDUCE_MEAN", "group_by_fields": ["resource.label.service"], "per_series_aligner": "ALIGN_RATE"}], "comparison": "COMPARISON_GT", "denominator_aggregations": [], "denominator_filter": null, "duration": "300s", "evaluation_missing_data": null, "filter": "resource.type=\"api\" AND resource.labels.service=\"mcp-rules-gateway\"", "forecast_options": [], "threshold_value": 0.02, "trigger": []}], "display_name": "Error rate > 2%"}], "display_name": "MCP Rules Gateway - High Error Rate", "documentation": [], "enabled": true, "notification_channels": [], "project": "texas-laws-personalinjury", "severity": null, "timeouts": null, "user_labels": null}, "after_unknown": {"alert_strategy": [{"notification_channel_strategy": [], "notification_rate_limit": []}], "conditions": [{"condition_absent": [], "condition_matched_log": [], "condition_monitoring_query_language": [], "condition_prometheus_query_language": [], "condition_threshold": [{"aggregations": [{"group_by_fields": [false]}], "denominator_aggregations": [], "forecast_options": [], "trigger": []}], "name": true}], "creation_record": true, "documentation": [], "id": true, "name": true, "notification_channels": []}, "before_sensitive": false, "after_sensitive": {"alert_strategy": [{"notification_channel_strategy": [], "notification_rate_limit": []}], "conditions": [{"condition_absent": [], "condition_matched_log": [], "condition_monitoring_query_language": [], "condition_prometheus_query_language": [], "condition_threshold": [{"aggregations": [{"group_by_fields": [false]}], "denominator_aggregations": [], "forecast_options": [], "trigger": []}]}], "creation_record": [], "documentation": [], "notification_channels": []}}}, {"address": "google_monitoring_alert_policy.gateway_latency", "mode": "managed", "type": "google_monitoring_alert_policy", "name": "gateway_latency", "provider_name": "registry.terraform.io/hashicorp/google", "change": {"actions": ["create"], "before": null, "after": {"alert_strategy": [{"auto_close": "1800s", "notification_channel_strategy": [], "notification_rate_limit": []}], "combiner": "OR", "conditions": [{"condition_absent": [], "condition_matched_log": [], "condition_monitoring_query_language": [], "condition_prometheus_query_language": [], "condition_threshold": [{"aggregations": [{"alignment_period": "60s", "cross_series_reducer": "REDUCE_PERCENTILE_95", "group_by_fields": ["resource.label.service"], "per_series_aligner": "ALIGN_DELTA"}], "comparison": "COMPARISON_GT", "denominator_aggregations": [], "denominator_filter": null, "duration": "300s", "evaluation_missing_data": null, "filter": "resource.type=\"api\" AND resource.labels.service=\"mcp-rules-gateway\"", "forecast_options": [], "threshold_value": 700, "trigger": []}], "display_name": "P95 latency > 700ms"}], "display_name": "MCP Rules Gateway - High Latency", "documentation": [], "enabled": true, "notification_channels": [], "project": "texas-laws-personalinjury", "severity": null, "timeouts": null, "user_labels": null}, "after_unknown": {"alert_strategy": [{"notification_channel_strategy": [], "notification_rate_limit": []}], "conditions": [{"condition_absent": [], "condition_matched_log": [], "condition_monitoring_query_language": [], "condition_prometheus_query_language": [], "condition_threshold": [{"aggregations": [{"group_by_fields": [false]}], "denominator_aggregations": [], "forecast_options": [], "trigger": []}], "name": true}], "creation_record": true, "documentation": [], "id": true, "name": true, "notification_channels": []}, "before_sensitive": false, "after_sensitive": {"alert_strategy": [{"notification_channel_strategy": [], "notification_rate_limit": []}], "conditions": [{"condition_absent": [], "condition_matched_log": [], "condition_monitoring_query_language": [], "condition_prometheus_query_language": [], "condition_threshold": [{"aggregations": [{"group_by_fields": [false]}], "denominator_aggregations": [], "forecast_options": [], "trigger": []}]}], "creation_record": [], "documentation": [], "notification_channels": []}}}, {"address": "google_project_service.apigateway", "mode": "managed", "type": "google_project_service", "name": "apigateway", "provider_name": "registry.terraform.io/hashicorp/google", "change": {"actions": ["no-op"], "before": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/apigateway.googleapis.com", "project": "texas-laws-personalinjury", "service": "apigateway.googleapis.com", "timeouts": null}, "after": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/apigateway.googleapis.com", "project": "texas-laws-personalinjury", "service": "apigateway.googleapis.com", "timeouts": null}, "after_unknown": {}, "before_sensitive": {}, "after_sensitive": {}}}, {"address": "google_project_service.servicecontrol", "mode": "managed", "type": "google_project_service", "name": "servicecontrol", "provider_name": "registry.terraform.io/hashicorp/google", "change": {"actions": ["no-op"], "before": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/servicecontrol.googleapis.com", "project": "texas-laws-personalinjury", "service": "servicecontrol.googleapis.com", "timeouts": null}, "after": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/servicecontrol.googleapis.com", "project": "texas-laws-personalinjury", "service": "servicecontrol.googleapis.com", "timeouts": null}, "after_unknown": {}, "before_sensitive": {}, "after_sensitive": {}}}, {"address": "google_project_service.servicemanagement", "mode": "managed", "type": "google_project_service", "name": "servicemanagement", "provider_name": "registry.terraform.io/hashicorp/google", "change": {"actions": ["no-op"], "before": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/servicemanagement.googleapis.com", "project": "texas-laws-personalinjury", "service": "servicemanagement.googleapis.com", "timeouts": null}, "after": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/servicemanagement.googleapis.com", "project": "texas-laws-personalinjury", "service": "servicemanagement.googleapis.com", "timeouts": null}, "after_unknown": {}, "before_sensitive": {}, "after_sensitive": {}}}], "output_changes": {"api_config_id": {"actions": ["no-op"], "before": "mcp-rules-config-prod", "after": "mcp-rules-config-prod", "after_unknown": false, "before_sensitive": false, "after_sensitive": false}, "api_gateway_id": {"actions": ["no-op"], "before": "mcp-rules-gateway-prod", "after": "mcp-rules-gateway-prod", "after_unknown": false, "before_sensitive": false, "after_sensitive": false}, "api_gateway_url": {"actions": ["no-op"], "before": "https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev", "after": "https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev", "after_unknown": false, "before_sensitive": false, "after_sensitive": false}, "env_var_secret_ids": {"actions": ["delete"], "before": {"mcp_rules_base_prod": null, "mcp_rules_base_staging": null}, "after": null, "after_unknown": false, "before_sensitive": false, "after_sensitive": false}, "env_var_secret_names": {"actions": ["delete"], "before": {"mcp_rules_base_prod": null, "mcp_rules_base_staging": null}, "after": null, "after_unknown": false, "before_sensitive": false, "after_sensitive": false}, "mcp_api_key_names": {"actions": ["delete"], "before": {"demo-tenant": "mcp-rules-api-demo-tenant", "pilot-smith": "mcp-rules-api-pilot-smith", "sandbox-test": "mcp-rules-api-sandbox-test"}, "after": null, "after_unknown": false, "before_sensitive": false, "after_sensitive": false}, "mcp_key_rotator_email": {"actions": ["delete"], "before": null, "after": null, "after_unknown": false, "before_sensitive": false, "after_sensitive": false}, "mcp_secret_ids": {"actions": ["delete"], "before": {"demo-tenant": null, "pilot-smith": null, "sandbox-test": null}, "after": null, "after_unknown": false, "before_sensitive": false, "after_sensitive": false}, "mcp_secret_names": {"actions": ["delete"], "before": {"demo-tenant": null, "pilot-smith": null, "sandbox-test": null}, "after": null, "after_unknown": false, "before_sensitive": false, "after_sensitive": false}, "prod_gateway_host": {"actions": ["no-op"], "before": "mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev", "after": "mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev", "after_unknown": false, "before_sensitive": false, "after_sensitive": false}, "ssl_certificate_id": {"actions": ["update"], "before": null, "after_unknown": true, "before_sensitive": false, "after_sensitive": false}}, "prior_state": {"format_version": "1.0", "terraform_version": "1.5.7", "values": {"outputs": {"api_config_id": {"sensitive": false, "value": "mcp-rules-config-prod", "type": "string"}, "api_gateway_id": {"sensitive": false, "value": "mcp-rules-gateway-prod", "type": "string"}, "api_gateway_url": {"sensitive": false, "value": "https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev", "type": "string"}, "env_var_secret_ids": {"sensitive": false, "value": {"mcp_rules_base_prod": null, "mcp_rules_base_staging": null}, "type": ["object", {"mcp_rules_base_prod": "string", "mcp_rules_base_staging": "string"}]}, "env_var_secret_names": {"sensitive": false, "value": {"mcp_rules_base_prod": null, "mcp_rules_base_staging": null}, "type": ["object", {"mcp_rules_base_prod": "string", "mcp_rules_base_staging": "string"}]}, "mcp_api_key_names": {"sensitive": false, "value": {"demo-tenant": "mcp-rules-api-demo-tenant", "pilot-smith": "mcp-rules-api-pilot-smith", "sandbox-test": "mcp-rules-api-sandbox-test"}, "type": ["object", {"demo-tenant": "string", "pilot-smith": "string", "sandbox-test": "string"}]}, "mcp_key_rotator_email": {"sensitive": false, "value": null, "type": "string"}, "mcp_secret_ids": {"sensitive": false, "value": {"demo-tenant": null, "pilot-smith": null, "sandbox-test": null}, "type": ["object", {"demo-tenant": "dynamic", "pilot-smith": "dynamic", "sandbox-test": "dynamic"}]}, "mcp_secret_names": {"sensitive": false, "value": {"demo-tenant": null, "pilot-smith": null, "sandbox-test": null}, "type": ["object", {"demo-tenant": "dynamic", "pilot-smith": "dynamic", "sandbox-test": "dynamic"}]}, "prod_gateway_host": {"sensitive": false, "value": "mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev", "type": "string"}, "ssl_certificate_id": {"sensitive": false, "value": null, "type": "string"}}, "root_module": {"resources": [{"address": "google_api_gateway_api.mcp_rules_api", "mode": "managed", "type": "google_api_gateway_api", "name": "mcp_rules_api", "provider_name": "registry.terraform.io/hashicorp/google-beta", "schema_version": 0, "values": {"api_id": "mcp-rules-gateway", "create_time": "2025-06-12T10:05:15.989929003Z", "display_name": "MCP Rules Engine API", "effective_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "id": "projects/texas-laws-personalinjury/locations/global/apis/mcp-rules-gateway", "labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "managed_service": "mcp-rules-gateway-3jnyyfifdjilf.apigateway.texas-laws-personalinjury.cloud.goog", "name": "projects/texas-laws-personalinjury/locations/global/apis/mcp-rules-gateway", "project": "texas-laws-personalinjury", "terraform_labels": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}, "timeouts": null}, "sensitive_values": {"effective_labels": {}, "labels": {}, "terraform_labels": {}}, "depends_on": ["google_project_service.apigateway", "google_project_service.servicecontrol", "google_project_service.servicemanagement"]}, {"address": "google_api_gateway_gateway.prod", "mode": "managed", "type": "google_api_gateway_gateway", "name": "prod", "provider_name": "registry.terraform.io/hashicorp/google-beta", "schema_version": 0, "values": {"api_config": "projects/122290401475/locations/global/apis/mcp-rules-gateway/configs/mcp-rules-config-prod-final", "default_hostname": "mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev", "display_name": "MCP Rules Gateway Production", "effective_labels": {}, "gateway_id": "mcp-rules-gateway-prod", "id": "projects/texas-laws-personalinjury/locations/us-central1/gateways/mcp-rules-gateway-prod", "labels": {}, "name": "projects/texas-laws-personalinjury/locations/us-central1/gateways/mcp-rules-gateway-prod", "project": "texas-laws-personalinjury", "region": "us-central1", "terraform_labels": {}, "timeouts": null}, "sensitive_values": {"effective_labels": {}, "labels": {}, "terraform_labels": {}}, "depends_on": ["google_api_gateway_api.mcp_rules_api", "google_api_gateway_api_config.mcp_rules_config", "google_project_service.apigateway", "google_project_service.servicecontrol", "google_project_service.servicemanagement"]}, {"address": "google_cloud_run_service_iam_member.gateway_invoker", "mode": "managed", "type": "google_cloud_run_service_iam_member", "name": "gateway_invoker", "provider_name": "registry.terraform.io/hashicorp/google", "schema_version": 0, "values": {"condition": [], "etag": "BwY4HLxGEyk=", "id": "v1/projects/texas-laws-personalinjury/locations/us-central1/services/mcp-prod/roles/run.invoker/serviceAccount:<EMAIL>", "location": "us-central1", "member": "serviceAccount:<EMAIL>", "project": "texas-laws-personalinjury", "role": "roles/run.invoker", "service": "v1/projects/texas-laws-personalinjury/locations/us-central1/services/mcp-prod"}, "sensitive_values": {"condition": []}}, {"address": "google_project_service.apigateway", "mode": "managed", "type": "google_project_service", "name": "apigateway", "provider_name": "registry.terraform.io/hashicorp/google", "schema_version": 0, "values": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/apigateway.googleapis.com", "project": "texas-laws-personalinjury", "service": "apigateway.googleapis.com", "timeouts": null}, "sensitive_values": {}}, {"address": "google_project_service.servicecontrol", "mode": "managed", "type": "google_project_service", "name": "servicecontrol", "provider_name": "registry.terraform.io/hashicorp/google", "schema_version": 0, "values": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/servicecontrol.googleapis.com", "project": "texas-laws-personalinjury", "service": "servicecontrol.googleapis.com", "timeouts": null}, "sensitive_values": {}}, {"address": "google_project_service.servicemanagement", "mode": "managed", "type": "google_project_service", "name": "servicemanagement", "provider_name": "registry.terraform.io/hashicorp/google", "schema_version": 0, "values": {"disable_dependent_services": true, "disable_on_destroy": false, "id": "texas-laws-personalinjury/servicemanagement.googleapis.com", "project": "texas-laws-personalinjury", "service": "servicemanagement.googleapis.com", "timeouts": null}, "sensitive_values": {}}]}}}, "configuration": {"provider_config": {"archive": {"name": "archive", "full_name": "registry.terraform.io/hashicorp/archive", "version_constraint": "~> 2.0"}, "google": {"name": "google", "full_name": "registry.terraform.io/hashicorp/google", "version_constraint": "~> 5.0", "expressions": {"project": {"references": ["var.project_id"]}, "region": {"references": ["var.region"]}}}, "google-beta": {"name": "google-beta", "full_name": "registry.terraform.io/hashicorp/google-beta", "version_constraint": "~> 5.0", "expressions": {"project": {"references": ["var.project_id"]}, "region": {"references": ["var.region"]}}}}, "root_module": {"outputs": {"api_config_id": {"expression": {"references": ["google_api_gateway_api_config.mcp_rules_config.api_config_id", "google_api_gateway_api_config.mcp_rules_config"]}, "description": "The ID of the API Gateway configuration"}, "api_gateway_id": {"expression": {"references": ["google_api_gateway_gateway.prod.gateway_id", "google_api_gateway_gateway.prod"]}, "description": "The ID of the API Gateway"}, "api_gateway_url": {"expression": {"references": ["google_api_gateway_gateway.prod.default_hostname", "google_api_gateway_gateway.prod"]}, "description": "The full URL of the API Gateway"}, "prod_gateway_host": {"expression": {"references": ["google_api_gateway_gateway.prod.default_hostname", "google_api_gateway_gateway.prod"]}, "description": "The hostname of the production API Gateway"}, "ssl_certificate_id": {"expression": {"references": ["google_compute_managed_ssl_certificate.mcp_rules_ssl.id", "google_compute_managed_ssl_certificate.mcp_rules_ssl"]}, "description": "The ID of the managed SSL certificate"}}, "resources": [{"address": "google_api_gateway_api.mcp_rules_api", "mode": "managed", "type": "google_api_gateway_api", "name": "mcp_rules_api", "provider_config_key": "google-beta", "expressions": {"api_id": {"constant_value": "mcp-rules-gateway"}, "display_name": {"constant_value": "MCP Rules Engine API"}, "labels": {"constant_value": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}}}, "schema_version": 0, "depends_on": ["google_project_service.apigateway", "google_project_service.servicecontrol", "google_project_service.servicemanagement"]}, {"address": "google_api_gateway_api_config.mcp_rules_config", "mode": "managed", "type": "google_api_gateway_api_config", "name": "mcp_rules_config", "provider_config_key": "google-beta", "expressions": {"api": {"references": ["google_api_gateway_api.mcp_rules_api.api_id", "google_api_gateway_api.mcp_rules_api"]}, "api_config_id": {"constant_value": "mcp-rules-config-prod"}, "display_name": {"constant_value": "MCP Rules Engine Production Config"}, "gateway_config": [{"backend_config": [{"google_service_account": {"references": ["var.backend_service_account"]}}]}], "labels": {"constant_value": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine", "version": "v1"}}, "openapi_documents": [{"document": [{"contents": {"references": ["local.openapi_prod"]}, "path": {"constant_value": "openapi.yaml"}}]}]}, "schema_version": 0, "depends_on": ["google_api_gateway_api.mcp_rules_api"]}, {"address": "google_api_gateway_gateway.prod", "mode": "managed", "type": "google_api_gateway_gateway", "name": "prod", "provider_config_key": "google-beta", "expressions": {"api_config": {"references": ["google_api_gateway_api_config.mcp_rules_config.id", "google_api_gateway_api_config.mcp_rules_config"]}, "display_name": {"constant_value": "MCP Rules Engine Production Gateway"}, "gateway_id": {"constant_value": "mcp-rules-gateway-prod"}, "labels": {"constant_value": {"environment": "production", "managed_by": "terraform", "service": "mcp-rules-engine"}}, "region": {"references": ["var.region"]}}, "schema_version": 0, "depends_on": ["google_api_gateway_api_config.mcp_rules_config"]}, {"address": "google_cloud_run_service_iam_member.gateway_invoker", "mode": "managed", "type": "google_cloud_run_service_iam_member", "name": "gateway_invoker", "provider_config_key": "google", "expressions": {"location": {"references": ["var.region"]}, "member": {"references": ["var.backend_service_account"]}, "role": {"constant_value": "roles/run.invoker"}, "service": {"constant_value": "mcp-prod"}}, "schema_version": 0}, {"address": "google_compute_managed_ssl_certificate.mcp_rules_ssl", "mode": "managed", "type": "google_compute_managed_ssl_certificate", "name": "mcp_rules_ssl", "provider_config_key": "google", "expressions": {"managed": [{"domains": {"references": ["var.api_gateway_domain"]}}], "name": {"constant_value": "mcp-rules-gateway-ssl"}}, "schema_version": 0}, {"address": "google_monitoring_alert_policy.gateway_error_rate", "mode": "managed", "type": "google_monitoring_alert_policy", "name": "gateway_error_rate", "provider_config_key": "google", "expressions": {"alert_strategy": [{"auto_close": {"constant_value": "1800s"}}], "combiner": {"constant_value": "OR"}, "conditions": [{"condition_threshold": [{"aggregations": [{"alignment_period": {"constant_value": "60s"}, "cross_series_reducer": {"constant_value": "REDUCE_MEAN"}, "group_by_fields": {"constant_value": ["resource.label.service"]}, "per_series_aligner": {"constant_value": "ALIGN_RATE"}}], "comparison": {"constant_value": "COMPARISON_GT"}, "duration": {"constant_value": "300s"}, "filter": {"constant_value": "resource.type=\"api\" AND resource.labels.service=\"mcp-rules-gateway\""}, "threshold_value": {"constant_value": 0.02}}], "display_name": {"constant_value": "Error rate > 2%"}}], "display_name": {"constant_value": "MCP Rules Gateway - High Error Rate"}, "notification_channels": {"constant_value": []}}, "schema_version": 0}, {"address": "google_monitoring_alert_policy.gateway_latency", "mode": "managed", "type": "google_monitoring_alert_policy", "name": "gateway_latency", "provider_config_key": "google", "expressions": {"alert_strategy": [{"auto_close": {"constant_value": "1800s"}}], "combiner": {"constant_value": "OR"}, "conditions": [{"condition_threshold": [{"aggregations": [{"alignment_period": {"constant_value": "60s"}, "cross_series_reducer": {"constant_value": "REDUCE_PERCENTILE_95"}, "group_by_fields": {"constant_value": ["resource.label.service"]}, "per_series_aligner": {"constant_value": "ALIGN_DELTA"}}], "comparison": {"constant_value": "COMPARISON_GT"}, "duration": {"constant_value": "300s"}, "filter": {"constant_value": "resource.type=\"api\" AND resource.labels.service=\"mcp-rules-gateway\""}, "threshold_value": {"constant_value": 700}}], "display_name": {"constant_value": "P95 latency > 700ms"}}], "display_name": {"constant_value": "MCP Rules Gateway - High Latency"}, "notification_channels": {"constant_value": []}}, "schema_version": 0}, {"address": "google_project_service.apigateway", "mode": "managed", "type": "google_project_service", "name": "apigateway", "provider_config_key": "google", "expressions": {"disable_dependent_services": {"constant_value": true}, "disable_on_destroy": {"constant_value": false}, "service": {"constant_value": "apigateway.googleapis.com"}}, "schema_version": 0}, {"address": "google_project_service.servicecontrol", "mode": "managed", "type": "google_project_service", "name": "servicecontrol", "provider_config_key": "google", "expressions": {"disable_dependent_services": {"constant_value": true}, "disable_on_destroy": {"constant_value": false}, "service": {"constant_value": "servicecontrol.googleapis.com"}}, "schema_version": 0}, {"address": "google_project_service.servicemanagement", "mode": "managed", "type": "google_project_service", "name": "servicemanagement", "provider_config_key": "google", "expressions": {"disable_dependent_services": {"constant_value": true}, "disable_on_destroy": {"constant_value": false}, "service": {"constant_value": "servicemanagement.googleapis.com"}}, "schema_version": 0}], "variables": {"api_gateway_domain": {"default": "rules.ailexlaw.com", "description": "Custom domain for the API Gateway (optional)"}, "backend_service_account": {"default": "<EMAIL>", "description": "Service account email for Cloud Run invoker permissions"}, "cloud_run_url": {"description": "Production Cloud Run base URL for the MCP Rules Engine service"}, "enable_monitoring": {"default": true, "description": "Enable monitoring and alerting resources"}, "environment": {"default": "production", "description": "Environment name (production, staging, development)"}, "gateway_timeout": {"default": 30, "description": "API Gateway timeout in seconds"}, "project_id": {"default": "texas-laws-personalinjury", "description": "Google Cloud Project ID for MCP Rules Engine (texas-laws-personalinjury)"}, "rate_limit_per_minute": {"default": 100, "description": "Rate limit per API key per minute"}, "region": {"default": "us-central1", "description": "Google Cloud region for resource deployment"}}}}, "relevant_attributes": [{"resource": "google_api_gateway_api_config.mcp_rules_config", "attribute": ["id"]}, {"resource": "google_compute_managed_ssl_certificate.mcp_rules_ssl", "attribute": ["id"]}, {"resource": "google_api_gateway_api.mcp_rules_api", "attribute": ["api_id"]}], "timestamp": "2025-06-22T07:37:59Z"}