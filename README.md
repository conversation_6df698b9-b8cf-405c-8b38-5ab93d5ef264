# PI Lawyer AI: Comprehensive Authentication & Authorization Strategy

[![OWASP ZAP Scan](https://github.com/Jpkay/pi_lawyer_ai/actions/workflows/zap.yml/badge.svg)](https://github.com/Jpkay/pi_lawyer_ai/actions/workflows/zap.yml)


## 1. Route Categories & Protection Strategy

### A. Public Routes (No Authentication Required)
- **Routes:**
  - `/` (landing page)
  - `/login`
  - `/register`
  - `/privacy-policy`, `/terms`, etc.
- **Protection:** None needed
- **Implementation:** Exclude from middleware

### B. Client Portal Routes (Client Authentication)
- **Routes:**
  - `/client-portal/*`
  - `/submit-case`
- **Protection:** Middleware + Client Role Check
- **Implementation:** Middleware + Client Layout

### C. Staff Routes (Staff Authentication)
- **Routes:**
  - `/dashboard/*`
  - `/cases/*`
  - `/documents/*`
  - `/clients/*`
- **Protection:** Middleware + Staff Role Check
- **Implementation:** Middleware + Authenticated Layout

### D. Admin-Only Routes (Partner Authentication)
- **Routes:**
  - `/admin/*`
  - `/settings/*`
  - `/billing/*`
  - `/users/*`
- **Protection:** Middleware + Partner Role Check
- **Implementation:** Middleware + Role-specific checks

## 2. Authentication Layers

### Middleware Protection
- Checks user session
- Validates user role
- Redirects unauthorized access

### API Route Protection
- Uses `withAuth` wrapper
- Validates user roles
- Prevents unauthorized API access

### Database Row Level Security (RLS)
- Filters data by `tenant_id`
- Enforces role-based access
- Provides an additional security layer

## 3. User Roles

Supported roles:
- `partner`: Full administrative access
- `attorney`: Case management, client interactions
- `paralegal`: Support tasks, document management
- `staff`: Limited administrative access
- `client`: Personal case portal access

## 4. Security Best Practices

- Minimal JWT claims
- Multiple authentication layers
- Tenant-based data isolation
- Service role for admin operations
- Comprehensive error handling

## 5. Database Schema Usage

### Tenant Data vs. Public Data

- **Tenant Data**: All tenant-specific tables (e.g., `cases`, `documents`, `clients`, `deadlines`, `assignments`, etc.) reside in the `tenants` schema.
  **Always use:**
  ```python
  .schema('tenants').from_('table_name')
  ```
  This ensures data isolation and prevents cross-tenant access.

- **Public Data**: Shared resources (e.g., `legal_templates`, reference tables) reside in the `public` schema (the default).
  **Always use:**
  ```python
  .from_('table_name')
  ```
  No explicit schema is needed for public data.

### Code Review & Contribution Guidelines

- All new queries for tenant data **must** specify `.schema('tenants')`.
- Code reviewers should reject PRs that do not follow this convention for tenant tables.
- Public/shared data should **not** specify the tenants schema.

### Example

```python
# Correct for tenant data
client.schema('tenants').from_('cases').select('*').eq('id', case_id).execute()

# Correct for public data
client.from_('legal_templates').select('*').execute()
```

### Why is this Important?

- Prevents bugs where data is read/written to the wrong schema.
- Ensures Row Level Security (RLS) and tenant isolation are always enforced.
- Makes migrations and multi-environment deployments safer and more predictable.

## 6. Database Trigger: `sync_auth_users`

This trigger is crucial for maintaining consistency between the Supabase `auth.users` table and the application's `tenants.users` table.

**Purpose:**
- Automatically creates or updates a corresponding record in `tenants.users` whenever a user is created or updated in `auth.users` (e.g., during sign-up or login).
- Extracts `tenant_id` and `role` from `auth.users.raw_user_meta_data` and populates the respective columns in `tenants.users`.
- Updates `tenants.users.last_login` timestamp upon user login.

**Key Implementation Details & Troubleshooting Notes:**
- **`SECURITY DEFINER`:** The associated function `public.sync_auth_user()` runs with `SECURITY DEFINER`. This is essential because the trigger needs to modify the `tenants.users` table, potentially bypassing the Row Level Security (RLS) policies that would apply to the invoking user (the one logging in). Running as the function owner ensures it has the necessary permissions. *Initial troubleshooting showed RLS errors when this function ran with default `SECURITY INVOKER` privileges.*
- **Lookup Logic:** The function **must** look up existing records in `tenants.users` using `WHERE auth_user_id = NEW.id`. It cannot assume the primary key (`id`) of `auth.users` matches the primary key (`id`) of `tenants.users`. *Initial troubleshooting showed "duplicate key" errors on the `tenants.users.email` constraint because the function incorrectly tried to INSERT a user that already existed but was linked via `auth_user_id`.*
- **Linking:** The `tenants.users` table uses an `auth_user_id` column to link back to the corresponding `auth.users.id`.

Understanding this trigger is vital for debugging login issues or problems related to user data synchronization between the authentication system and the application's tenant data.

## 7. Authentication Architecture

### Authentication Components
| Component | File | Responsibility |
|-----------|------|----------------|
| **API Authentication** | `/src/lib/auth-helpers.ts` | Central mechanism for API route protection |
| **Security Context** | `/src/lib/security/context.tsx` | Device trust management and fingerprinting |
| **Supabase Client** | `/src/lib/supabase/client.ts` | Supabase initialization and session management |
| **Security Forensics** | `/src/lib/security/forensics.ts` | Security event logging and monitoring |
| **JWT Debugging** | `/src/lib/debug/jwtrole.ts` | Development tools for JWT issues |
| **Route Protection** | `/src/middleware.ts` | Route-level authentication and access control |

### Key Authentication Functions
- `requireAuth(allowedRoles?)`: Validate user session and role permissions
- `withAuth(handler, allowedRoles)`: Wrap API route handlers for authentication
- `createServiceClient()`: Create admin-level Supabase client for server operations

### Usage Example
```typescript
// In an API route
import { withAuth } from '@/lib/auth-helpers'
import { NextResponse } from 'next/server'

export const GET = withAuth(async (req, user) => {
  // User is already authenticated and role-checked
  // Access user.id, user.role, user.tenantId

  return NextResponse.json({ message: "Authenticated!" })
}, ['partner', 'attorney']) // Only these roles can access
```

## 8. Supabase SSR & Cookie Handling Strategy

### Authentication Client Creation
- **User Routes**: `createServerClientForUser(req)`
  - Uses Supabase Anon Key
  - Respects Row Level Security (RLS)
  - Synchronous cookie access via `req.cookies`

- **Service Routes**: `createServiceClient(req)`
  - Uses Supabase Service Role Key
  - Bypasses RLS for admin/backend operations
  - Synchronous cookie access via `req.cookies`

### Cookie Handling Approach
- **Synchronous Access**: Use `req.cookies.get(name)?.value`
- **Async Modifications**: Use `cookies().set()` and `cookies().delete()`
- **Consistent Across Routes**: Unified strategy for user and service routes

### Authentication Wrappers
- **`withAuth`**:
  - Protects user-facing routes
  - Validates user roles
  - Uses RLS-enabled client

- **`withServiceRole`**:
  - Protects admin/backend routes
  - Bypasses user authentication
  - Uses service role client

### Key Benefits
- Resolves Next.js and Supabase SSR cookie compatibility
- Provides secure, consistent authentication
- Supports granular access control
- Maintains separation between user and admin operations

### Security Considerations
- Always pass `NextRequest` to client creation methods
- Use service role clients sparingly and with caution
- Implement additional role-based checks as needed

## 9. Environment Setup

### Required Environment Variables
- `NEXT_PUBLIC_SUPABASE_URL`
- `SUPABASE_SERVICE_KEY`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`

### Local Development
1. Copy `.env.example` to `.env.local`
2. Fill in required environment variables
3. Run `npm install`
4. Start development server: `npm run dev`

## 10. Deployment Considerations

- Always use environment-specific configurations
- Rotate service account keys regularly
- Monitor authentication logs
- Implement additional monitoring for suspicious activities

## 11. Troubleshooting

### Common Authentication Issues
- Verify JWT claims
- Check role assignments
- Validate tenant configurations

### Debugging
- Use `createServiceClient()` for admin investigations
- Leverage Supabase logs
- Implement comprehensive logging

## 12. Documentation

For detailed documentation on the system architecture, see:
- [Authentication Architecture](/docs/authentication-architecture.md)
- [Document Processing Pipeline](/docs/document-processing.md)
- [CopilotKit Integration Guide](/docs/copilotkit-integration.md)
- [GCS Storage Architecture](/docs/gcs-storage-architecture.md)
- [Deadline Extraction and Retrieval](./deadlines_extraction_readme.md)
- [**MCP Rules Engine Production Readiness Guide**](/docs/PRODUCTION_READINESS.md) ✅
- [API Key Verification Report](/docs/API_KEY_VERIFICATION.md) ✅

## 13. Contributing

**Schema Enforcement Policy:**
- All Supabase queries for tenant data must use `.schema('tenants')`.
- This is a hard requirement for all code merged into main.
- Automated tests and code review will enforce this policy.

Please read our security guidelines before contributing to the authentication system.

## 14. Testing Authentication

### Authentication Route Testing

We've created a comprehensive test authentication route at `/api/test-auth` to verify different authentication scenarios:

#### Endpoints
- `GET /api/test-auth`: Accessible to all authenticated roles
  - Verifies basic authentication
  - Returns user information

- `POST /api/test-auth`: Partner-only endpoint
  - Tests role-based access control
  - Only accessible to users with 'partner' role

- `PUT /api/test-auth`: Staff roles endpoint
  - Checks access for staff-level roles
  - Accessible to partners, attorneys, paralegals, and staff

- `DELETE /api/test-auth`: Client-specific endpoint
  - Validates client-role access restrictions

#### Testing Procedure
1. Log in with different user roles
2. Send requests to each endpoint
3. Verify expected behavior:
   - Authorized users receive successful responses
   - Unauthorized users receive 401 Unauthorized errors

#### Example Test Scenarios
```bash
# Partner user
curl -X GET /api/test-auth     # Should succeed
curl -X POST /api/test-auth    # Should succeed
curl -X PUT /api/test-auth     # Should succeed
curl -X DELETE /api/test-auth  # Should fail

# Client user
curl -X GET /api/test-auth     # Should succeed
curl -X POST /api/test-auth    # Should fail
curl -X PUT /api/test-auth     # Should fail
curl -X DELETE /api/test-auth  # Should succeed
```

### Debugging Authentication
- Use browser network tools to inspect response headers
- Check server logs for detailed authentication errors
- Verify JWT claims and role assignments

## 15. Testing

For detailed testing information and guidance, please see the [TESTING.md](./TESTING.md) file.

## 16. Agents Overview

The PI Lawyer AI system utilizes a sophisticated multi-agent architecture organized into two primary systems:

1. **Original Agent System** - Foundation system with specialized agents for various legal tasks
2. **LangGraph Agent System** - Newer system focused on bidirectional insights generation

### Key Agents in the Original System

- **Supervisor Agent** - Routes user inquiries to specialized agents
- **Intake Agent** - Guides users through the intake process for new cases
- **Research Agent** - Finds relevant laws and legal precedents
- **Document Agent** - Assists with drafting legal documents
- **Event Agent** - Manages events and calendar functionalities
- **Calendar Agents** - Handle calendar events and deadline management

### Key Components in the LangGraph System

- **LangGraph Supervisor Agent** - Coordinates the insight generation workflow
- **Swarm Agent System** - Collaborative specialized agents for insights generation
  - Activity Insight Agent
  - Case Insight Agent
  - Document Insight Agent
  - Deadline Insight Agent

**For comprehensive documentation of all agents, their responsibilities, and how they interact within the system, please refer to the [Agents Architecture Documentation](./docs/agents-architecture.md).**

### 4. Calendar Deadline Agent
- **Purpose**: Specifically focused on managing deadlines associated with calendar events.
- **Configuration**:
  - **Services**: Managed through `calendar-deadline-service.ts`, which handles the logic for deadlines.

### Analysis of Agent Organization
- **Consistency**: Each agent follows a consistent naming and configuration pattern, which is crucial for maintainability.
- **Separation of Concerns**: Each agent has a defined role and responsibility, simplifying the codebase.
- **Communication**: Agents communicate through defined API routes, ensuring seamless interaction between frontend components and backend services.

### Recommendations for Improvement
1. **Centralized Agent Management**: Consider creating a centralized directory or service for managing all agents.
2. **Testing and Validation**: Implement unit tests for each agent to ensure they handle various scenarios correctly.
3. **Documentation**: Enhance documentation for each agent, detailing their purpose, configuration, and usage patterns.
4. **Monitoring and Logging**: Set up monitoring for each agent to track performance and issues.

## 17. Rate Limiting and Resource Controls

The application implements a comprehensive rate limiting and resource control system to manage document uploads effectively. This ensures that tenants can only upload documents within their specified quotas and limits.

### Key Features:
- **Tenant Quotas**: Each tenant has specific limits on daily uploads, monthly uploads, maximum document size, and concurrent processing.
- **Rate Limit Middleware**: Applied to the document upload endpoint to check tenant limits before processing uploads.
- **Real-time Tracking**: Utilizes Redis for real-time counters to enforce limits immediately.
- **Admin Interface**: Accessible at `/admin/tenant-quotas`, where administrators can view and modify tenant quotas and monitor resource usage.

### Admin Interface for Quota Management
The admin interface for managing tenant quotas is available at `/admin/tenant-quotas`. This page allows administrators to:
- View current tenant quotas and resource usage.
- Modify resource limits for each tenant based on their subscription plans.
- Monitor overall system resource consumption through analytics.

### Usage Example
To check if a tenant can upload a document, the `RateLimitService` is used, which checks the tenant's current usage against their quotas and returns whether the upload is allowed or not.

## Advanced Features

### Proactive Activity Insights
For detailed information about our intelligent activity tracking and insights feature, please refer to [Proactive Activity Insights Documentation](/docs/proactive-activity-insights.md).

### Subscription Management System
The application includes a comprehensive subscription management system that supports:
- Flexible subscription plans with different tiers (Starter, Professional, Enterprise)
- Add-on modules for additional practice areas, states, and features
- 14-day free trials with extension capabilities
- Usage tracking and quota enforcement
- Notification system for trial expiration and quota limits

For detailed documentation on the subscription management system, see:
- [Subscription Management Documentation](/frontend/docs/SUBSCRIPTION_MANAGEMENT.md)

## Staging Environment

This project includes a dedicated staging environment backed by its own Supabase project, completely isolated from production.

- **Staging Project Ref**: `btwaueeckvylrlrnbvgt`
- **Link CLI**:
  ```bash
  supabase link --project-ref btwaueeckvylrlrnbvgt
  ```
- **Push migrations**:
  ```bash
  supabase db push --include-all
  ```
- **Environment Variables**: Use a separate `.env.staging` file with the same variables as production, pointing to staging:
  - `NEXT_PUBLIC_SUPABASE_URL`
  - `NEXT_PUBLIC_SUPABASE_ANON_KEY`
  - `SUPABASE_SERVICE_KEY`
  - (other env vars as needed)
- **Isolation**: Staging uses its own schemas (`public`, `tenants`, `security`) and will not affect production data.

## Troubleshooting: Auth.User Triggers

### Issue: Permission Denied for tenants Schema
- Upon user signup, the `sync_auth_user` trigger failed with the error:
  ```
  permission denied for schema tenants
  ```
- This occurred because the internal Supabase role (`supabase_auth_admin`) lacked access to the `tenants` schema.

### Solution:
1. Grant necessary permissions:
   ```sql
   GRANT USAGE ON SCHEMA tenants TO supabase_auth_admin;
   GRANT SELECT, INSERT, UPDATE ON TABLE tenants.users TO supabase_auth_admin;
   ```
2. Updated the `sync_auth_user` trigger function to support the 3-step flow:
   - No action on `INSERT` if `tenant_id` is `NULL`.
   - On `UPDATE` when `tenant_id` is set, insert or update `tenants.users`.
   - Robust null checks and safe UUID casting when extracting metadata.
3. Re-enabled other triggers (`on_auth_user_created`, `on_user_change`, `on_user_role_change`) after validation.

This ensures new users can sign up without errors and are synced to `tenants.users` once their `tenant_id` is available.

## Code Quality and Automated Checks

To maintain code consistency, prevent errors, and ensure a smoother development process, this project utilizes a multi-layered approach for code quality checks:

1.  **IDE Integration (Real-time Feedback):**
    *   **Tools:** ESLint, Prettier (via VS Code extensions or similar).
    *   **Function:** Provides immediate feedback on TypeScript errors, linting issues, and formatting problems directly within your code editor as you type or save files.

2.  **Manual Project-Wide Checks (NPM Scripts):**
    *   Located in `frontend/package.json`.
    *   `npm run lint`: Runs ESLint across the entire frontend project and attempts to automatically fix identified issues (`--fix`).
    *   `npm run type-check`: Runs the TypeScript compiler (`tsc --noEmit`) to check for type errors across the entire frontend project without generating JavaScript output. Useful for a full validation before major changes or commits.

3.  **Automated Pre-Commit Checks (Husky + lint-staged):**
    *   **Tools:** `husky`, `lint-staged`.
    *   **Function:** Before any `git commit` is finalized, this automatically runs `eslint --fix` *only* on the files that have been staged (`git add ...`).
    *   **Outcome:** If ESLint finds errors that cannot be fixed automatically, the commit process is **blocked**, forcing errors to be corrected before they enter the version history. This enforces incremental fixing.

4.  **Continuous Integration Checks (GitHub Actions):**
    *   **Configuration:** `.github/workflows/ci.yml`.
    *   **Function:** Automatically triggers on pushes or pull requests to the `main` and `develop` branches.
    *   **Process:** Runs on GitHub's servers, performs a clean setup, installs dependencies (`npm ci`), and executes `npm run lint` and `npm run type-check` for the frontend project.
    *   **Outcome:** Acts as a final validation step. If any checks fail, the workflow run is marked as failed in the GitHub Actions tab, providing a clear signal that the pushed code has issues.

## MCP Rules Engine Production Deployment

✅ **COMPLETED**: Automated Vercel to API Gateway cut-over successfully deployed.

### Production API Gateway
- **URL**: `https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev`
- **Health Check**: `GET /health` (public, no authentication)
- **MCP Endpoint**: `POST /mcp/run` (requires `x-api-key` header)
- **Status**: Active and processing legal deadline calculations

### Key Components
- **API Gateway**: Google Cloud API Gateway with proper backend authentication
- **Backend Service**: Cloud Run service `mcp-prod` in `texas-laws-personalinjury` project
- **Authentication**: API key-based with keys stored in Secret Manager
- **Monitoring**: Error rate and latency alerts configured

### Environment Variables
```bash
MCP_RULES_BASE=https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev
FEATURE_MCP_RULES_ENGINE=true
```

For detailed deployment and troubleshooting information, see [API_GATEWAY_PROD.md](docs/API_GATEWAY_PROD.md).
