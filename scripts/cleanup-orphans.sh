#!/bin/bash
set -euo pipefail

# Cleanup Orphaned Terraform Resources
# Removes orphaned Cloud Function resources from Terraform state without affecting cloud resources
# This is a one-time cleanup for the MCP Rules Engine migration

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TERRAFORM_DIR="$SCRIPT_DIR/../infra/terraform"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Change to terraform directory
cd "$TERRAFORM_DIR"

log_info "Starting Terraform state cleanup for orphaned Cloud Function resources..."

# List of orphaned resource IDs to remove
ORPHANED_RESOURCES=(
    "google_storage_bucket_object.function_source"
    "google_storage_bucket.function_source_bucket"
)

# Track if any resources were removed
RESOURCES_REMOVED=0

# Remove each orphaned resource from state
for resource in "${ORPHANED_RESOURCES[@]}"; do
    log_info "Checking resource: $resource"
    
    # Check if resource exists in state
    if terraform state list | grep -q "^$resource$"; then
        log_warning "Removing orphaned resource from state: $resource"
        terraform state rm "$resource"
        RESOURCES_REMOVED=$((RESOURCES_REMOVED + 1))
        log_success "Removed: $resource"
    else
        log_info "Resource not found in state (already clean): $resource"
    fi
done

# Summary
if [ $RESOURCES_REMOVED -eq 0 ]; then
    log_success "No orphaned resources found - Terraform state is already clean"
else
    log_success "Cleanup complete - removed $RESOURCES_REMOVED orphaned resource(s) from state"
    log_info "Note: No cloud resources were deleted, only Terraform state was cleaned"
fi

# Verify state is clean by running a plan
log_info "Verifying Terraform state is clean..."

# Use a placeholder URL for the plan check
CLOUD_RUN_URL="${CLOUD_RUN_URL:-https://mcp-prod-placeholder.uc.a.run.app}"

set +e  # Don't exit on non-zero exit codes for plan check
terraform plan \
    -var="cloud_run_url=$CLOUD_RUN_URL" \
    -detailed-exitcode \
    -out=cleanup-verification.tfplan

PLAN_EXIT_CODE=$?
set -e

if [ $PLAN_EXIT_CODE -eq 0 ]; then
    log_success "✅ Terraform state is clean - no changes detected"
elif [ $PLAN_EXIT_CODE -eq 2 ]; then
    log_warning "⚠️ Terraform detected changes after cleanup"
    log_info "This may be expected if there are legitimate infrastructure updates"
    terraform show cleanup-verification.tfplan
else
    log_error "❌ Terraform plan failed with exit code $PLAN_EXIT_CODE"
    exit $PLAN_EXIT_CODE
fi

# Clean up temporary plan file
rm -f cleanup-verification.tfplan

log_success "🎉 Orphan cleanup completed successfully"
exit 0
