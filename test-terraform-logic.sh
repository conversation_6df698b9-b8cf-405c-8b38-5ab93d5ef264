#!/bin/bash
set -euo pipefail

echo "🧪 Testing Terraform sync logic..."

# Test the jq logic from the workflow
UNEXPECTED_CHANGES=$(jq -r '
  .resource_changes[]? |
  select(.change.actions[] | contains("create") or contains("update") or contains("delete")) |
  select(.type | test("^google_(monitoring_alert_policy|api_gateway_api_config|compute_managed_ssl_certificate)$") | not) |
  select(.address != "google_api_gateway_gateway.prod") |
  select(.type | test("^google_(project_iam_|secret_manager_secret)") | not) |
  .address
' infra/terraform/plan.json)

echo "📋 All resource changes:"
jq -r '.resource_changes[]? | select(.change.actions[] | contains("create") or contains("update") or contains("delete")) | "\(.type): \(.address)"' infra/terraform/plan.json

echo ""
if [ -n "$UNEXPECTED_CHANGES" ]; then
  echo "❌ Unexpected changes detected:"
  echo "$UNEXPECTED_CHANGES"
  exit 1
else
  echo "✅ Only expected resource types detected (monitoring, API config, SSL cert, gateway metadata)"
  echo "🚀 Workflow would auto-apply these changes"
fi
