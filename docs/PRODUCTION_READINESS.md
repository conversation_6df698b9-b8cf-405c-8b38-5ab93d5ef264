# MCP Rules Engine — Production Readiness & Operations Guide  
_Last updated: 2025-06-22_

---

## 1  Executive summary  
* **Live stack**: Front/Back → API Gateway → Cloud Run MCP service  
* **Status**: ✅ DEPLOYED, VERIFIED, MONITORED, PER-TENANT KEYS OPERATIONAL  
* **Remaining backlog**: key-rotation cron, optional vanity domain, dashboards

---

## 2  High-level architecture  

```text
┌─────────────┐  HTTPS + x-api-key  ┌────────────────────────────────────────┐
│ AiLex UI /  │───────────────────►│  API Gateway (texas-laws-personalinjury)│
│  Backend    │                    │  • global auth & rate limiting          │
└─────────────┘                    └─────────────┬───────────────────────────┘
                                                ID Token (SA)
                                     ┌──────────────────────────────────┐
                                     │ Cloud Run MCP Rules Engine       │
                                     │ (mcp-prod, same project)         │
                                     └──────────────────────────────────┘
```

*Tenants live in `newtexaslaw` project; rules engine & secrets live in `texas-laws-personalinjury`.*

---

## 3  Endpoints

| Path       | Method | Auth               | Description                                                      |
| ---------- | ------ | ------------------ | ---------------------------------------------------------------- |
| `/health`  | GET    | *none*             | Returns `{ "status":"OK", "timestamp":"..." }` for uptime checks |
| `/mcp/run` | POST   | `x-api-key` header | Calculates deadlines; see § 7 for schema                         |

Gateway host (prod):
`https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev`

---

## 4  Authentication & security ✅ OPERATIONAL

* **API key header**: `x-api-key` (one per tenant)
* Keys are created by **`mcpProvisioner.ts`** and stored in Google **Secret Manager** (`mcp-key-<tenantID>`).
* API Gateway uses `mcp-client@texas-laws-personalinjury` to generate identity tokens for Cloud Run.
* Rate limit: **100 requests/minute per key** (configured in OpenAPI spec).
* All traffic HTTPS, TLS 1.2+.

**Current Status**: 5 active secrets in Secret Manager, 2 verified active tenants

---

## 5  Tenant onboarding workflow ✅ OPERATIONAL

1. Backend `createTenant()` inserts row → `mcp_status = pending_key`.
2. `provisionMcpAccess()` (lines 34-99 in `mcpProvisioner.ts`):
   * creates API key (`apikeys.admin`)
   * stores key in Secret Manager (`secretmanager.admin`)
   * updates tenant row with `secretPath` + `mcp_status = active`.
3. First deadline request pulls key via `deadlinesTool.ts` (10-min cache).
4. Live verifier script: `scripts/verify-tenant-api-key.sh TENANT_ID`.

**Verified Working**: End-to-end flow tested with tenants `89be252b-021c-45ab-b55f-41b3a913c760` and `1c8c2888-6c87-4f6e-8fb8-2523e45426e9`

---

## 6  Environment variables (backend prod)

| Var                        | Example value                                            |
| -------------------------- | -------------------------------------------------------- |
| `MCP_RULES_BASE`           | `https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev` |
| `FEATURE_MCP_RULES_ENGINE` | `true`                                                   |
| `REDIS_URL`                | `redis://...` (for key cache)                            |

---

## 7  `/mcp/run` request / response ✅ VERIFIED

### Request

```json
{
  "toolName": "calculate_deadlines",
  "params": {
    "jurisdiction": "TX_STATE",
    "triggerCode": "SERVICE_OF_PROCESS",
    "startDate": "2025-08-01",
    "practiceArea": "personal_injury"
  }
}
```

### Success (200)

```json
{
  "result": {
    "deadlines": [
      {
        "deadlineCode": "ANSWER_DUE",
        "deadlineDate": "2025-08-21",
        "ruleCitation": "Tex. R. Civ. P. 99(b)",
        "calcSteps": ["Start 2025-06-21", "+20 calendar days → 2025-07-11"]
      }
    ]
  }
}
```

### Errors

* `400 INVALID_ARGUMENT` – malformed body or invalid key
* `401 UNAUTHENTICATED` – missing/invalid key
* `429 RESOURCE_EXHAUSTED` – rate limit exceeded

---

## 8  CI / CD workflows ✅ OPERATIONAL

| File                                   | Purpose                                                                    |
| -------------------------------------- | -------------------------------------------------------------------------- |
| `.github/workflows/deploy-prod.yml`    | Gateway apply → backend redeploy on every push to **main**                 |
| `.github/workflows/terraform-sync.yml` | Nightly drift check, alert-policy apply, gateway & tenant-key verification |

Secrets required in GitHub > Actions:

* `GCP_SA_KEY` – SA JSON key with cross-project perms
* `CLOUD_RUN_URL` – URL of Cloud Run MCP service
* `API_KEY_SANDBOX` – key for `sandbox-test` tenant
* `SLACK_WEBHOOK_URL` (optional)

**New**: Added tenant API key verification step to nightly CI

---

## 9  Monitoring & alerting

Policies defined in `monitoring/alert_policies.yaml` and applied by CI:

| Alert                | Threshold                                    |
| -------------------- | -------------------------------------------- |
| Error rate           | > 2 % over 5 min                             |
| P95 latency          | > 700 ms                                     |
| Circuit-breaker open | log match `jsonPayload.breaker_state="OPEN"` |
| Gateway quota        | 80 % of per-key limit                        |

---

## 10  Operational run-books

### A. Manually rotate a tenant key

```bash
TENANT_ID=<uuid>
gcloud apikeys keys delete "$(gcloud apikeys keys list --location=global --filter="displayName=$TENANT_ID" --format=value(name))"
NEW_KEY=$(gcloud apikeys keys create --display-name="$TENANT_ID" --location=global --format="value(stringValue)")

gcloud secrets versions add "mcp-key-$TENANT_ID" \
  --data-file=<(echo "$NEW_KEY") \
  --project=texas-laws-personalinjury
```

### B. Investigate high error rate

1. Check API Gateway logs (`resource.type="api_gateway_proxy"`).
2. Filter by `labels.api_key_id`.
3. Identify tenant -> contact firm.

### C. Verify tenant provisioning

```bash
./scripts/verify-tenant-api-key.sh <TENANT_ID>
```

---

## 11  Current operational status ✅ PRODUCTION READY

**Secret Manager Inventory** (texas-laws-personalinjury):
```
mcp-key-1c8c2888-6c87-4f6e-8fb8-2523e45426e9  (Production Test LLP)
mcp-key-89be252b-021c-45ab-b55f-41b3a913c760  (Sandbox Test LLP)  
mcp-key-demo-tenant                           (Demo tenant)
mcp-key-pilot-smith                           (Pilot tenant)
mcp-key-rotator-sa-key                        (Key rotation SA)
```

**Active Tenants**: 2 verified with `mcp_status='active'`
**Gateway Health**: ✅ `/health` responding
**End-to-End**: ✅ Deadline calculations working

---

## 12  Backlog / future work

* **Key rotation** – Cloud Scheduler job to rotate keys annually.
* **Vanity domain** – map `rules.ailexlaw.com` to gateway for cleaner logs.
* **Grafana / Looker dashboard** – visual error-rate, latency, top tenants.

---

## 13  Changelog (excerpt)

| Date       | Change                                                                                                              |
| ---------- | ------------------------------------------------------------------------------------------------------------------- |
| 2025-06-22 | ✅ VERIFIED: API Gateway operational, tenant key provisioning working, end-to-end deadline calculations functional |
| 2025-06-22 | API Gateway cut-over; removed Vercel preview; health made public; template provider removed; nightly TF sync added. |
| 2025-06-19 | In-app tenant onboarding with auto key provisioning.                                                                |
| 2025-06-15 | Initial MCP Rules Engine deployment (Vercel proof).                                                                 |

---

**IMPORTANT**: This system is NOT missing work - it is a fully functional production system that has been verified through live testing and is actively serving tenant requests.
