# Cross-Project Infrastructure Management

## Overview

The MCP Rules Engine infrastructure spans multiple Google Cloud projects, which creates permission challenges for automated Terraform management. This document explains why certain resources are managed manually and provides guidance for operational procedures.

## Project Structure

### texas-laws-personalinjury (Rules Engine Project)
- **Purpose**: Hosts the MCP Rules Engine infrastructure
- **Terraform Managed**: ✅ Yes
- **Resources**:
  - API Gateway and configurations
  - Cloud Run services
  - Monitoring and alerting
  - SSL certificates
  - Environment variable secrets

### newtexaslaw-************* (Tenant Data Project)  
- **Purpose**: Hosts tenant data and tenant-specific resources
- **Terraform Managed**: ❌ No (Manual)
- **Resources**:
  - Tenant API keys
  - Tenant-specific secrets
  - Service accounts for tenant operations
  - IAM roles and bindings

## Why Manual Management?

### Permission Constraints
The current service account used by Terraform lacks sufficient permissions in the tenant project to:
- Create and manage API keys (`apikeys.keys.*`)
- Create service accounts (`iam.serviceAccounts.create`)
- Manage custom IAM roles (`iam.roles.*`)
- Create Secret Manager secrets (`secretmanager.secrets.create`)

### Cross-Project Complexity
Managing resources across multiple projects requires:
- Complex provider aliases
- Cross-project service account impersonation
- Coordinated permission management
- Increased risk of permission drift

## Current Operational Status

### ✅ What's Working (Automated via Terraform)
- **API Gateway**: Fully deployed and operational
- **Cloud Run Backend**: MCP Rules Engine service running
- **Monitoring**: Alert policies for error rate and latency
- **Environment Variables**: MCP_RULES_BASE secrets managed

### ✅ What's Working (Manual Management)
- **Tenant API Keys**: Created and stored in Secret Manager
- **End-to-End Flow**: Tenant creation → API key → deadline calculation
- **Production Usage**: System actively serving tenant requests

### 📋 Manual Procedures

#### Creating Tenant API Keys
```bash
# 1. Create API key
TENANT_ID="new-tenant-id"
API_KEY=$(gcloud services api-keys create \
  --display-name="MCP Rules Engine API Key - $TENANT_ID" \
  --project=newtexaslaw-************* \
  --format="value(keyString)")

# 2. Store in Secret Manager (texas-laws-personalinjury project)
echo "$API_KEY" | gcloud secrets create "mcp-key-$TENANT_ID" \
  --data-file=- \
  --project=texas-laws-personalinjury \
  --labels="tenant=$TENANT_ID,service=mcp-rules-engine,managed_by=manual"

# 3. Update tenant record in database
# (This is typically done via the application's tenant onboarding flow)
```

#### Rotating Tenant API Keys
```bash
# 1. Create new API key
NEW_API_KEY=$(gcloud services api-keys create \
  --display-name="MCP Rules Engine API Key - $TENANT_ID (rotated)" \
  --project=newtexaslaw-************* \
  --format="value(keyString)")

# 2. Update secret version
echo "$NEW_API_KEY" | gcloud secrets versions add "mcp-key-$TENANT_ID" \
  --data-file=- \
  --project=texas-laws-personalinjury

# 3. Delete old API key (after verification)
gcloud services api-keys delete $OLD_KEY_ID --project=newtexaslaw-*************
```

## Terraform State Management

### Clean Nightly Plans
The Terraform configuration has been updated to exclude tenant-project resources, ensuring:
- ✅ Nightly `terraform plan` runs show no unexpected changes
- ✅ No cross-project permission errors in CI/CD
- ✅ Focus on manageable infrastructure components

### Removed Resources
The following resources were removed from Terraform management:
- `google_apikeys_key.mcp_api_keys`
- `google_secret_manager_secret.mcp_api_keys`
- `google_secret_manager_secret_version.mcp_api_key_versions`
- `google_project_iam_custom_role.mcp_key_manager`
- `google_service_account.mcp_key_rotator`
- All tenant-project provider aliases

## Future Considerations

### Option 1: Enhanced Permissions
Grant additional permissions to the Terraform service account:
- **Pros**: Full automation, consistent state management
- **Cons**: Broad permissions, security complexity, cross-project coordination

### Option 2: Separate Terraform Configurations
Create dedicated Terraform configurations per project:
- **Pros**: Isolated permissions, clearer boundaries
- **Cons**: Multiple state files, coordination complexity

### Option 3: Continue Manual Management
Maintain current approach with improved tooling:
- **Pros**: Clear separation, minimal permissions, proven operational
- **Cons**: Manual steps, potential for drift

## Verification

The system's operational status can be verified using:

```bash
# Test API Gateway health
curl https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev/health

# Test tenant API key functionality
./scripts/verify-tenant-api-key.sh <TENANT_ID>

# Check secret inventory
gcloud secrets list --project=texas-laws-personalinjury --filter="name:mcp-key"
```

## Conclusion

The current hybrid approach (Terraform for core infrastructure, manual for tenant resources) provides:
- ✅ **Operational Stability**: Core system is fully functional
- ✅ **Clean Automation**: Nightly CI/CD runs without permission errors  
- ✅ **Security**: Minimal required permissions
- ✅ **Maintainability**: Clear separation of concerns

This approach will continue until cross-project permissions can be safely expanded or alternative architectures are implemented.
